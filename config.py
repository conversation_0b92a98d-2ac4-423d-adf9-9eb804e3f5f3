"""
币安API配置模块
功能：管理敏感信息、全局配置和路径设置
注意：请先在项目根目录创建 .env 文件存储API密钥
"""

import os
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv  # 用于加载.env文件中的环境变量

# --------------------------
# 环境变量加载
# --------------------------
# 加载项目根目录下的.env文件
load_dotenv()

# --------------------------
# 目录路径配置
# --------------------------
# 项目根目录
BASE_DIR = Path(__file__).resolve().parent

# 数据存储目录 (自动创建如果不存在)
DATA_DIR = BASE_DIR / 'data'
DATA_DIR.mkdir(exist_ok=True, parents=True)

# 日志目录
LOG_DIR = BASE_DIR / 'logs'
LOG_DIR.mkdir(exist_ok=True)

# --------------------------
# 币安API配置
# --------------------------
# 从环境变量获取API密钥（确保已在.env文件中设置）
BINANCE_API_KEY = os.getenv('BINANCE_API_KEY')
BINANCE_API_SECRET = os.getenv('BINANCE_API_SECRET')

# API基础地址
BINANCE_API_URL = "https://fapi.binance.com"  # 期货API地址
BINANCE_TESTNET_URL = "https://testnet.binancefuture.com"  # 测试网地址

# 交易对和K线类型配置
SYMBOL = 'BTCUSDT'       # 交易对符号
INTERVAL = '1d'          # K线间隔 (日线)
KLINE_LIMIT = 120        # 每次请求获取的K线数量 (最近120天数据)
USE_TESTNET = False      # 是否使用测试网络

# --------------------------
# 文件命名配置
# --------------------------
# 获取当前日期（可从环境变量覆盖）
current_date = os.getenv('RUN_DATE', datetime.now().strftime("%Y%m%d"))

# 原始数据文件名格式：BTCUSDT_日线原始数据_20240717.csv
RAW_DATA_FILENAME = f"{SYMBOL}_日线原始数据_{current_date}.csv"

# 技术指标文件名格式：BTCUSDT_技术指标分析_20240717.csv
INDICATORS_FILENAME = f"{SYMBOL}_技术指标分析_{current_date}.csv"

# 组合数据文件名格式：BTCUSDT_组合数据_20240717.csv
COMBINED_FILENAME = f"{SYMBOL}_组合数据_{current_date}.csv"

# 分析报告文件名格式：BTCUSDT_交易分析报告_20240717.txt
REPORT_FILENAME = f"{SYMBOL}_交易分析报告_{current_date}.txt"

# 日志文件名格式：app_20240717.log
LOG_FILENAME = f"app_{current_date}.log"

# --------------------------
# TA-Lib 指标参数配置
# --------------------------
# 移动平均线参数
MA_SHORT_TERM = 20    # 短期均线周期
MA_LONG_TERM = 50     # 长期均线周期

# MACD参数
MACD_FAST = 12        # 快速EMA周期
MACD_SLOW = 26        # 慢速EMA周期
MACD_SIGNAL = 9       # 信号线周期

# RSI参数
RSI_PERIOD = 14       # RSI计算周期

# 布林带参数
BB_PERIOD = 20        # 布林带周期
BB_STD_DEV = 2        # 标准差倍数

# 随机指标参数
STOCH_FASTK = 14      # 快速K线周期
STOCH_SLOWK = 3       # 慢速K线周期
STOCH_SLOWD = 3       # 慢速D线周期

# ATR参数
ATR_PERIOD = 14       # 平均真实波幅周期

# --------------------------
# 交易策略参数
# --------------------------
# 信号阈值
RSI_OVERBOUGHT = 70   # RSI超买阈值
RSI_OVERSOLD = 30     # RSI超卖阈值

# 综合信号权重
SIGNAL_WEIGHTS = {
    'MA': 0.3,
    'MACD': 0.25,
    'RSI': 0.2,
    'BB': 0.15,
    'VOLUME': 0.1
}

# --------------------------
# 日志配置
# --------------------------
LOG_LEVEL = "INFO"    # 日志级别 (DEBUG/INFO/WARNING/ERROR/CRITICAL)
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# --------------------------
# 验证关键配置
# --------------------------
if not BINANCE_API_KEY or not BINANCE_API_SECRET:
    raise ValueError("未检测到币安API密钥! 请检查.env文件配置")

# 测试输出配置信息（实际使用时可注释掉）
if __name__ == '__main__':
    print("\n=== 配置信息 ===")
    print(f"API密钥: {'已设置' if BINANCE_API_KEY else '未设置'}")
    print(f"使用测试网络: {'是' if USE_TESTNET else '否'}")
    print(f"交易对: {SYMBOL}")
    print(f"K线间隔: {INTERVAL}")

    print("\n路径配置:")
    print(f"数据目录: {DATA_DIR}")
    print(f"日志目录: {LOG_DIR}")

    print("\n文件配置:")
    print(f"原始数据文件: {RAW_DATA_FILENAME}")
    print(f"技术指标文件: {INDICATORS_FILENAME}")
    print(f"组合数据文件: {COMBINED_FILENAME}")
    print(f"分析报告文件: {REPORT_FILENAME}")

    print("\n技术指标参数:")
    print(f"移动平均线: MA{MA_SHORT_TERM}/MA{MA_LONG_TERM}")
    print(f"MACD: {MACD_FAST}-{MACD_SLOW}-{MACD_SIGNAL}")
    print(f"RSI周期: {RSI_PERIOD}")
    print(f"布林带: {BB_PERIOD}期 {BB_STD_DEV}倍标准差")

    print("\n策略参数:")
    print(f"RSI阈值: 超买>{RSI_OVERBOUGHT} 超卖<{RSI_OVERSOLD}")
    print("信号权重:", SIGNAL_WEIGHTS)