"""
时间周期技术指标参数分析
分析不同时间周期下技术指标参数的适用性
"""

import pandas as pd
import numpy as np

def analyze_timeframe_parameters():
    """分析不同时间周期的技术指标参数适用性"""
    
    print("=" * 70)
    print("时间周期技术指标参数适用性分析")
    print("=" * 70)
    
    # 当前使用的参数
    current_params = {
        'MA_SHORT': 20,
        'MA_LONG': 50,
        'MACD': (12, 26, 9),
        'RSI': 14,
        'BB': 20,
        'ATR': 14,
        'Stoch': 14
    }
    
    # 不同时间周期的建议参数
    timeframe_recommendations = {
        '15分钟线': {
            'description': '短线交易，需要更敏感的参数',
            'data_points': 480,
            'time_span': '5天',
            'recommended_params': {
                'MA_SHORT': 10,  # 更短的均线
                'MA_LONG': 30,   # 相对较短的长期均线
                'MACD': (8, 17, 9),  # 更敏感的MACD
                'RSI': 9,        # 更敏感的RSI
                'BB': 15,        # 更短的布林带周期
                'ATR': 10,       # 更短的ATR周期
                'Stoch': 9       # 更敏感的随机指标
            },
            'rationale': {
                'MA': '短线交易需要更快响应价格变化',
                'MACD': '更敏感的参数能更快捕捉短期趋势变化',
                'RSI': '短周期RSI能更好识别短期超买超卖',
                'BB': '较短周期能更好反映短期波动',
                'ATR': '短期波动性测量',
                'Stoch': '短期动量指标'
            }
        },
        '1小时线': {
            'description': '短中期分析，平衡敏感性和稳定性',
            'data_points': 240,
            'time_span': '10天',
            'recommended_params': {
                'MA_SHORT': 12,
                'MA_LONG': 26,
                'MACD': (9, 21, 7),
                'RSI': 11,
                'BB': 16,
                'ATR': 12,
                'Stoch': 11
            },
            'rationale': {
                'MA': '适中的参数平衡响应速度和稳定性',
                'MACD': '标准参数的变体，适合小时级别分析',
                'RSI': '稍短的周期提高敏感性',
                'BB': '适中的周期',
                'ATR': '适中的波动性测量',
                'Stoch': '平衡的动量指标'
            }
        },
        '4小时线': {
            'description': '中期分析，使用标准参数',
            'data_points': 180,
            'time_span': '30天',
            'recommended_params': current_params,  # 使用当前标准参数
            'rationale': {
                'MA': '标准参数适合中期趋势分析',
                'MACD': '经典参数组合',
                'RSI': '标准14周期',
                'BB': '标准20周期',
                'ATR': '标准14周期',
                'Stoch': '标准参数'
            }
        },
        '日线': {
            'description': '长期分析，可使用更长周期参数',
            'data_points': 120,
            'time_span': '120天',
            'recommended_params': {
                'MA_SHORT': 21,  # 稍长的短期均线
                'MA_LONG': 55,   # 稍长的长期均线
                'MACD': (12, 26, 9),  # 保持标准MACD
                'RSI': 14,       # 标准RSI
                'BB': 21,        # 稍长的布林带
                'ATR': 14,       # 标准ATR
                'Stoch': 14      # 标准随机指标
            },
            'rationale': {
                'MA': '稍长的均线减少噪音，更好反映长期趋势',
                'MACD': '标准参数适合日线分析',
                'RSI': '标准参数',
                'BB': '稍长周期减少假信号',
                'ATR': '标准参数',
                'Stoch': '标准参数'
            }
        }
    }
    
    # 分析每个时间周期
    for timeframe, config in timeframe_recommendations.items():
        print(f"\n📊 {timeframe}")
        print("-" * 50)
        print(f"描述: {config['description']}")
        print(f"数据量: {config['data_points']}条 ({config['time_span']})")
        
        print(f"\n当前参数 vs 建议参数:")
        current = current_params
        recommended = config['recommended_params']
        
        for param_name in ['MA_SHORT', 'MA_LONG', 'RSI', 'BB', 'ATR', 'Stoch']:
            current_val = current.get(param_name, 'N/A')
            recommended_val = recommended.get(param_name, 'N/A')
            
            if current_val != recommended_val:
                status = "🔄 建议调整"
            else:
                status = "✅ 当前合适"
                
            print(f"  {param_name:10}: 当前{current_val:2} → 建议{recommended_val:2} {status}")
        
        # MACD特殊处理
        current_macd = current['MACD']
        recommended_macd = recommended['MACD']
        if current_macd != recommended_macd:
            status = "🔄 建议调整"
        else:
            status = "✅ 当前合适"
        print(f"  {'MACD':10}: 当前{current_macd} → 建议{recommended_macd} {status}")
        
        print(f"\n调整理由:")
        for indicator, reason in config['rationale'].items():
            print(f"  • {indicator}: {reason}")
    
    # 总结建议
    print(f"\n" + "=" * 70)
    print("总结与建议")
    print("=" * 70)
    
    print(f"\n✅ 当前实现状态:")
    print(f"• 所有时间周期使用统一的技术指标参数")
    print(f"• 数据量充足，所有指标都能正常计算")
    print(f"• 指标值在合理范围内，计算结果正确")
    
    print(f"\n🔄 优化建议:")
    print(f"• 考虑为不同时间周期使用不同的指标参数")
    print(f"• 短时间周期(15分钟、1小时)使用更敏感的参数")
    print(f"• 长时间周期(日线)可使用稍长的参数减少噪音")
    print(f"• 4小时线使用当前标准参数即可")
    
    print(f"\n⚠️  注意事项:")
    print(f"• 参数调整会影响指标的敏感性和稳定性")
    print(f"• 建议在实际使用前进行回测验证")
    print(f"• 可以提供参数配置选项让用户选择")
    
    print(f"\n💡 实现建议:")
    print(f"• 在config.py中为每个时间周期定义专门的参数")
    print(f"• 在ta_calculator.py中根据时间周期选择对应参数")
    print(f"• 保持当前实现作为默认选项，新增优化选项")

if __name__ == "__main__":
    analyze_timeframe_parameters()
