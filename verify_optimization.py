"""
验证参数优化和200条数据的实现效果
"""

import pandas as pd
import numpy as np
from pathlib import Path

def verify_optimization():
    """验证优化后的参数和数据量"""
    
    print("=" * 70)
    print("参数优化和数据量验证报告")
    print("=" * 70)
    
    # 检查的时间周期
    timeframes = {
        '15分钟线': {
            'file': 'data/BTCUSDT_15分钟线组合数据_20250717.csv',
            'expected_params': {'MA_SHORT': 10, 'MA_LONG': 30, 'RSI': 9, 'BB': 15}
        },
        '1小时线': {
            'file': 'data/BTCUSDT_1小时线组合数据_20250717.csv', 
            'expected_params': {'MA_SHORT': 12, 'MA_LONG': 26, 'RSI': 11, 'BB': 16}
        },
        '4小时线': {
            'file': 'data/BTCUSDT_4小时线组合数据_20250717.csv',
            'expected_params': {'MA_SHORT': 20, 'MA_LONG': 50, 'RSI': 14, 'BB': 20}
        },
        '日线': {
            'file': 'data/BTCUSDT_日线组合数据_20250717.csv',
            'expected_params': {'MA_SHORT': 21, 'MA_LONG': 55, 'RSI': 14, 'BB': 21}
        }
    }
    
    results = {}
    
    for timeframe, config in timeframes.items():
        print(f"\n📊 {timeframe} 验证:")
        print("-" * 50)
        
        filepath = config['file']
        expected_params = config['expected_params']
        
        try:
            # 检查文件是否存在
            if not Path(filepath).exists():
                print(f"❌ 文件不存在: {filepath}")
                results[timeframe] = 'file_not_found'
                continue
            
            # 读取数据
            df = pd.read_csv(filepath)
            data_count = len(df)
            
            # 验证数据量
            print(f"数据量: {data_count}条")
            if data_count == 200:
                print("✅ 数据量正确 (200条)")
            else:
                print(f"⚠️  数据量异常 (期望200条，实际{data_count}条)")
            
            # 验证列结构
            print(f"列数: {len(df.columns)}列")
            
            # 检查关键技术指标列
            key_indicators = ['MA20', 'MA50', 'RSI', 'MACD', 'BB_Upper', 'BB_Lower', 'ATR']
            missing_indicators = []
            for indicator in key_indicators:
                if indicator not in df.columns:
                    missing_indicators.append(indicator)
            
            if missing_indicators:
                print(f"❌ 缺少指标列: {missing_indicators}")
            else:
                print("✅ 所有关键指标列存在")
            
            # 验证指标有效性
            print("\n指标有效性检查:")
            latest = df.iloc[-1]
            
            # MA指标
            ma20 = latest.get('MA20')
            ma50 = latest.get('MA50')
            if pd.notna(ma20) and pd.notna(ma50):
                print(f"  MA20: {ma20:.2f}, MA50: {ma50:.2f} ✅")
            else:
                print(f"  MA指标: 存在无效值 ❌")
            
            # RSI指标
            rsi = latest.get('RSI')
            if pd.notna(rsi) and 0 <= rsi <= 100:
                print(f"  RSI: {rsi:.2f} ✅")
            else:
                print(f"  RSI: {rsi} ❌")
            
            # 布林带指标
            bb_upper = latest.get('BB_Upper')
            bb_lower = latest.get('BB_Lower')
            if pd.notna(bb_upper) and pd.notna(bb_lower) and bb_upper > bb_lower:
                print(f"  布林带: 上轨{bb_upper:.2f} > 下轨{bb_lower:.2f} ✅")
            else:
                print(f"  布林带: 异常 ❌")
            
            # 验证参数优化效果（通过比较不同时间周期的指标差异）
            print(f"\n参数优化验证:")
            
            # 计算MA20和MA50的有效数据量
            ma20_valid = df['MA20'].notna().sum()
            ma50_valid = df['MA50'].notna().sum()
            
            # 根据不同参数，有效数据量应该不同
            expected_ma20_valid = 200 - expected_params['MA_SHORT'] + 1
            expected_ma50_valid = 200 - expected_params['MA_LONG'] + 1
            
            print(f"  MA20有效数据: {ma20_valid}条 (期望≥{expected_ma20_valid})")
            print(f"  MA50有效数据: {ma50_valid}条 (期望≥{expected_ma50_valid})")
            
            if ma20_valid >= expected_ma20_valid * 0.9 and ma50_valid >= expected_ma50_valid * 0.9:
                print(f"  ✅ 参数优化生效")
            else:
                print(f"  ⚠️  参数优化可能未生效")
            
            # 时间范围验证
            first_time = df.iloc[0]['open_time']
            last_time = df.iloc[-1]['open_time']
            print(f"\n时间范围: {first_time} 至 {last_time}")
            
            results[timeframe] = 'success'
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            results[timeframe] = 'error'
    
    # 对比不同时间周期的差异
    print(f"\n" + "=" * 70)
    print("时间周期对比分析")
    print("=" * 70)
    
    # 读取所有成功的数据进行对比
    comparison_data = {}
    for timeframe, status in results.items():
        if status == 'success':
            try:
                filepath = timeframes[timeframe]['file']
                df = pd.read_csv(filepath)
                latest = df.iloc[-1]
                comparison_data[timeframe] = {
                    'MA20': latest.get('MA20'),
                    'MA50': latest.get('MA50'),
                    'RSI': latest.get('RSI'),
                    'MACD': latest.get('MACD'),
                    'data_count': len(df)
                }
            except:
                pass
    
    if len(comparison_data) >= 2:
        print("\n最新指标值对比:")
        print(f"{'时间周期':<10} {'MA20':<10} {'MA50':<10} {'RSI':<8} {'MACD':<10} {'数据量':<8}")
        print("-" * 60)
        
        for timeframe, data in comparison_data.items():
            ma20 = f"{data['MA20']:.2f}" if pd.notna(data['MA20']) else "N/A"
            ma50 = f"{data['MA50']:.2f}" if pd.notna(data['MA50']) else "N/A"
            rsi = f"{data['RSI']:.2f}" if pd.notna(data['RSI']) else "N/A"
            macd = f"{data['MACD']:.4f}" if pd.notna(data['MACD']) else "N/A"
            count = data['data_count']
            
            print(f"{timeframe:<10} {ma20:<10} {ma50:<10} {rsi:<8} {macd:<10} {count:<8}")
    
    # 总结
    print(f"\n" + "=" * 70)
    print("验证总结")
    print("=" * 70)
    
    success_count = sum(1 for status in results.values() if status == 'success')
    total_count = len(results)
    
    print(f"\n✅ 成功验证: {success_count}/{total_count} 个时间周期")
    
    if success_count == total_count:
        print(f"🎉 所有优化都已成功实现!")
        print(f"   • 数据量已调整为200条")
        print(f"   • 技术指标参数已针对不同时间周期优化")
        print(f"   • 所有指标计算正常")
    else:
        failed_timeframes = [tf for tf, status in results.items() if status != 'success']
        print(f"⚠️  部分时间周期存在问题: {', '.join(failed_timeframes)}")
    
    return results

if __name__ == "__main__":
    verify_optimization()
