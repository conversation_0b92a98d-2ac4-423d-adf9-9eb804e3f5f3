"""
直接测试币安API获取最新数据
"""

from binance.um_futures import UMFutures
from datetime import datetime
import os
from dotenv import load_dotenv

def test_api_direct():
    """直接测试API获取最新数据"""
    
    print("=" * 60)
    print("直接测试币安API")
    print("=" * 60)
    
    # 加载环境变量
    load_dotenv()
    api_key = os.getenv('BINANCE_API_KEY')
    api_secret = os.getenv('BINANCE_API_SECRET')
    
    # 创建客户端
    if api_key and api_secret:
        client = UMFutures(key=api_key, secret=api_secret)
    else:
        client = UMFutures()
    
    try:
        print("📊 测试1: 获取最新200条1小时K线数据")
        print("-" * 40)
        
        # 方法1: 只指定limit
        klines1 = client.klines('BTCUSDT', '1h', limit=200)
        print(f"方法1结果: 获取到 {len(klines1)} 条数据")
        
        if klines1:
            first_time = datetime.fromtimestamp(klines1[0][0]/1000).strftime('%Y-%m-%d %H:%M:%S')
            last_time = datetime.fromtimestamp(klines1[-1][0]/1000).strftime('%Y-%m-%d %H:%M:%S')
            print(f"时间范围: {first_time} 至 {last_time}")
            
            # 显示最后5条数据
            print(f"\n最后5条数据:")
            for i in range(-5, 0):
                kline = klines1[i]
                time_str = datetime.fromtimestamp(kline[0]/1000).strftime('%Y-%m-%d %H:%M')
                open_price = float(kline[1])
                high_price = float(kline[2])
                low_price = float(kline[3])
                close_price = float(kline[4])
                print(f"{time_str}: 开{open_price:.2f} 高{high_price:.2f} 低{low_price:.2f} 收{close_price:.2f}")
        
        print(f"\n📊 测试2: 获取当前时间前200条数据")
        print("-" * 40)
        
        # 方法2: 指定endTime为当前时间
        import time
        current_time = int(time.time() * 1000)  # 当前时间戳（毫秒）
        
        klines2 = client.klines('BTCUSDT', '1h', limit=200, endTime=current_time)
        print(f"方法2结果: 获取到 {len(klines2)} 条数据")
        
        if klines2:
            first_time = datetime.fromtimestamp(klines2[0][0]/1000).strftime('%Y-%m-%d %H:%M:%S')
            last_time = datetime.fromtimestamp(klines2[-1][0]/1000).strftime('%Y-%m-%d %H:%M:%S')
            print(f"时间范围: {first_time} 至 {last_time}")
            
            # 显示最后5条数据
            print(f"\n最后5条数据:")
            for i in range(-5, 0):
                kline = klines2[i]
                time_str = datetime.fromtimestamp(kline[0]/1000).strftime('%Y-%m-%d %H:%M')
                open_price = float(kline[1])
                high_price = float(kline[2])
                low_price = float(kline[3])
                close_price = float(kline[4])
                print(f"{time_str}: 开{open_price:.2f} 高{high_price:.2f} 低{low_price:.2f} 收{close_price:.2f}")
        
        # 对比两种方法的差异
        if klines1 and klines2:
            print(f"\n🔍 对比分析:")
            print("-" * 40)
            
            last_time1 = datetime.fromtimestamp(klines1[-1][0]/1000)
            last_time2 = datetime.fromtimestamp(klines2[-1][0]/1000)
            
            print(f"方法1最后时间: {last_time1}")
            print(f"方法2最后时间: {last_time2}")
            
            if last_time1 == last_time2:
                print("✅ 两种方法获取的数据时间一致")
            else:
                print("⚠️ 两种方法获取的数据时间不一致")
                time_diff = (last_time2 - last_time1).total_seconds() / 3600
                print(f"时间差异: {time_diff:.1f} 小时")
        
        print(f"\n📊 测试3: 获取最新几条数据（包含当前未完成的K线）")
        print("-" * 40)
        
        # 方法3: 获取最新5条数据
        klines3 = client.klines('BTCUSDT', '1h', limit=5)
        print(f"方法3结果: 获取到 {len(klines3)} 条数据")
        
        if klines3:
            print(f"最新5条数据:")
            for kline in klines3:
                time_str = datetime.fromtimestamp(kline[0]/1000).strftime('%Y-%m-%d %H:%M')
                open_price = float(kline[1])
                high_price = float(kline[2])
                low_price = float(kline[3])
                close_price = float(kline[4])
                volume = float(kline[5])
                print(f"{time_str}: 开{open_price:.2f} 高{high_price:.2f} 低{low_price:.2f} 收{close_price:.2f} 量{volume:.0f}")
        
        # 检查当前时间
        print(f"\n🕐 时间信息:")
        print("-" * 40)
        current_dt = datetime.now()
        print(f"当前系统时间: {current_dt.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"当前小时: {current_dt.hour}")
        print(f"当前分钟: {current_dt.minute}")
        
        if klines3:
            last_kline_time = datetime.fromtimestamp(klines3[-1][0]/1000)
            print(f"最新K线时间: {last_kline_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 计算时间差
            time_diff_minutes = (current_dt - last_kline_time).total_seconds() / 60
            print(f"时间差: {time_diff_minutes:.0f} 分钟")
            
            if time_diff_minutes < 60:
                print("✅ 数据是最新的（1小时内）")
            else:
                print("⚠️ 数据可能不是最新的")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_api_direct()
