"""
检查市场数据的准确性
"""

from binance.um_futures import UMFutures
from datetime import datetime
import pandas as pd
import os
from dotenv import load_dotenv

def check_market_data():
    """检查当前市场数据和我们获取的数据"""
    
    print("=" * 60)
    print("市场数据准确性检查")
    print("=" * 60)
    
    # 加载环境变量
    load_dotenv()
    api_key = os.getenv('BINANCE_API_KEY')
    api_secret = os.getenv('BINANCE_API_SECRET')
    
    # 创建客户端
    if api_key and api_secret:
        client = UMFutures(key=api_key, secret=api_secret)
    else:
        client = UMFutures()  # 公开数据不需要密钥
    
    try:
        # 1. 获取当前价格信息
        print("📊 当前市场数据:")
        print("-" * 40)
        
        ticker = client.ticker_24hr_price_change('BTCUSDT')
        current_price = float(ticker['lastPrice'])
        high_24h = float(ticker['highPrice'])
        low_24h = float(ticker['lowPrice'])
        open_24h = float(ticker['openPrice'])
        change_pct = float(ticker['priceChangePercent'])
        
        print(f"当前价格: ${current_price:,.2f}")
        print(f"24h最高价: ${high_24h:,.2f}")
        print(f"24h最低价: ${low_24h:,.2f}")
        print(f"24h开盘价: ${open_24h:,.2f}")
        print(f"24h涨跌幅: {change_pct:+.2f}%")
        
        # 2. 获取最新的1小时K线数据
        print(f"\n🕐 最新1小时K线数据:")
        print("-" * 40)
        
        klines = client.klines('BTCUSDT', '1h', limit=10)
        
        print(f"{'时间':<20} {'开盘':<10} {'最高':<10} {'最低':<10} {'收盘':<10}")
        print("-" * 70)
        
        latest_data = []
        for kline in klines[-10:]:  # 最新10条
            open_time = datetime.fromtimestamp(kline[0]/1000).strftime('%Y-%m-%d %H:%M')
            open_price = float(kline[1])
            high_price = float(kline[2])
            low_price = float(kline[3])
            close_price = float(kline[4])
            
            print(f"{open_time:<20} {open_price:<10.2f} {high_price:<10.2f} {low_price:<10.2f} {close_price:<10.2f}")
            
            latest_data.append({
                'time': open_time,
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price
            })
        
        # 3. 检查我们保存的数据
        print(f"\n📁 检查本地保存的数据:")
        print("-" * 40)
        
        local_file = 'data/BTCUSDT_1小时线组合数据_20250717.csv'
        if os.path.exists(local_file):
            df = pd.read_csv(local_file)
            print(f"本地数据量: {len(df)}条")
            print(f"时间范围: {df.iloc[0]['open_time']} 至 {df.iloc[-1]['open_time']}")
            
            # 显示最新几条本地数据
            print(f"\n本地最新5条数据:")
            print(f"{'时间':<20} {'开盘':<10} {'最高':<10} {'最低':<10} {'收盘':<10}")
            print("-" * 70)
            
            for i in range(-5, 0):
                row = df.iloc[i]
                time_str = row['open_time'][:16]  # 只显示到分钟
                open_price = row['开盘价']
                high_price = row['最高价']
                low_price = row['最低价']
                close_price = row['收盘价']
                
                print(f"{time_str:<20} {open_price:<10.2f} {high_price:<10.2f} {low_price:<10.2f} {close_price:<10.2f}")
            
            # 4. 对比分析
            print(f"\n🔍 数据对比分析:")
            print("-" * 40)
            
            # 检查今日最高最低价
            today_str = datetime.now().strftime('%Y-%m-%d')
            today_data = df[df['open_time'].str.contains(today_str)]
            
            if len(today_data) > 0:
                local_high = today_data['最高价'].max()
                local_low = today_data['最低价'].min()
                
                print(f"API今日最高价: ${high_24h:,.2f}")
                print(f"本地今日最高价: ${local_high:,.2f}")
                print(f"差异: ${abs(high_24h - local_high):,.2f}")
                print()
                print(f"API今日最低价: ${low_24h:,.2f}")
                print(f"本地今日最低价: ${local_low:,.2f}")
                print(f"差异: ${abs(low_24h - local_low):,.2f}")
                
                # 判断数据是否准确
                high_diff = abs(high_24h - local_high)
                low_diff = abs(low_24h - local_low)
                
                if high_diff < 100 and low_diff < 100:  # 差异小于100美元认为正常
                    print(f"\n✅ 数据基本准确")
                else:
                    print(f"\n❌ 数据存在较大差异，可能需要重新获取")
                    
                    # 提供重新获取建议
                    print(f"\n💡 建议:")
                    print(f"1. 检查系统时间是否准确")
                    print(f"2. 重新运行数据获取程序")
                    print(f"3. 确认网络连接正常")
            else:
                print(f"本地数据中没有找到今日数据")
        else:
            print(f"本地数据文件不存在: {local_file}")
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        print(f"可能的原因:")
        print(f"1. 网络连接问题")
        print(f"2. API密钥配置问题")
        print(f"3. 币安API服务异常")

if __name__ == "__main__":
    check_market_data()
