# BTCUSDT 日线技术分析系统

这是一个自动化的BTCUSDT日线K线数据获取、技术指标计算和分析报告生成系统，专为DeepSeek AI分析优化。

## 功能特性

- 🔄 **自动数据获取**: 从币安交易所获取最新1000条BTCUSDT日线K线数据
- 📊 **技术指标计算**: 使用TA-Lib计算多种技术指标
- 🔀 **数据整合**: 将原始数据和技术指标合并为完整数据集
- 📝 **智能报告**: 生成易于DeepSeek理解的交易分析报告

## 技术指标

### 趋势指标
- **MA20/MA50**: 20日和50日移动平均线
- **MACD**: 异同移动平均线 (12-26-9)
- **ADX**: 平均趋向指数

### 震荡指标
- **RSI**: 相对强弱指标 (14日)
- **Stochastic**: 随机指标 (14-3-3)

### 波动性指标
- **Bollinger Bands**: 布林带 (20日, 2倍标准差)
- **ATR**: 平均真实波幅 (14日)

### 成交量指标
- **OBV**: 能量潮指标

### 信号分析
- **MA信号**: 金叉/死叉分析
- **MACD信号**: 看涨/看跌分析
- **RSI信号**: 超买/超卖区域分析
- **布林带信号**: 突破上下轨分析
- **综合信号**: 多指标加权评估

## 安装配置

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置API密钥
在项目根目录创建 `.env` 文件：
```
BINANCE_API_KEY=your_api_key_here
BINANCE_API_SECRET=your_api_secret_here
```

### 3. 运行程序
```bash
python main.py
```

## 使用说明

运行程序后，选择以下选项：

1. **执行完整分析流程** - 运行完整的数据获取→指标计算→数据合并→报告生成流程
2. **显示文件路径信息** - 查看生成文件的位置和状态
3. **运行测试模式** - 测试各个模块的功能
4. **退出** - 退出程序

## 输出文件

系统会在 `data/` 目录下生成以下文件：

- `BTCUSDT_日线原始数据_YYYYMMDD.csv` - 原始K线数据
- `BTCUSDT_技术指标分析_YYYYMMDD.csv` - 技术指标计算结果
- `BTCUSDT_组合数据_YYYYMMDD.csv` - 原始数据+技术指标的完整数据集
- `BTCUSDT_交易分析报告_YYYYMMDD.txt` - 易于DeepSeek理解的分析报告

## 项目结构

```
day_binance/
├── main.py                    # 主程序入口
├── config.py                  # 配置管理
├── binance_client.py          # 币安API客户端
├── ta_calculator.py           # 技术指标计算
├── combined_data_processor.py # 数据合并处理
├── report_generator.py        # 分析报告生成
├── requirements.txt           # 依赖包列表
├── .env                       # API密钥配置
├── data/                      # 数据输出目录
└── logs/                      # 日志目录
```

## 注意事项

1. **API密钥安全**: 请妥善保管您的币安API密钥，不要提交到版本控制系统
2. **网络连接**: 确保网络可以访问币安API (https://fapi.binance.com)
3. **时间同步**: 系统时间需要准确，币安API要求时间误差在30秒内
4. **数据更新**: 每次运行会获取最新的1000条日线数据

## 故障排除

如果遇到问题，请检查：
- API密钥是否正确配置
- 网络连接是否正常
- 系统时间是否准确
- 所有依赖包是否正确安装

## DeepSeek AI 使用建议

生成的分析报告专为DeepSeek AI优化，包含：
- 结构化的技术指标数据
- 清晰的信号分析
- 具体的交易建议
- 风险管理提示

将报告全文发送给DeepSeek AI可获得更详细的市场分析和交易策略建议。
