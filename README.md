# BTCUSDT 多时间周期技术分析系统

这是一个自动化的BTCUSDT多时间周期K线数据获取、技术指标计算和分析报告生成系统，专为DeepSeek AI分析优化。

## 功能特性

- 🔄 **多时间周期数据获取**: 支持15分钟线、1小时线、4小时线、日线数据获取
- 📊 **技术指标计算**: 使用TA-Lib计算多种技术指标
- 🔀 **数据整合**: 将原始数据和技术指标合并为完整数据集
- 📝 **智能报告**: 生成易于DeepSeek理解的交易分析报告
- ⚡ **灵活配置**: 根据不同时间周期自动调整数据获取量

## 支持的时间周期

| 选项 | 时间周期 | 数据量 | 时间范围 | 适用场景 | 优化参数 |
|------|----------|--------|----------|----------|----------|
| 1 | 15分钟线 | 200条 | 最近2天 | 短线交易、日内分析 | MA10/30, RSI9, BB15 |
| 2 | 1小时线 | 200条 | 最近8天 | 短中期分析、波段交易 | MA12/26, RSI11, BB16 |
| 3 | 4小时线 | 200条 | 最近33天 | 中期趋势分析 | MA20/50, RSI14, BB20 |
| 4 | 日线 | 200条 | 最近200天 | 长期趋势分析 | MA21/55, RSI14, BB21 |

## 技术指标

### 趋势指标
- **MA20/MA50**: 20日和50日移动平均线
- **MACD**: 异同移动平均线 (12-26-9)
- **ADX**: 平均趋向指数

### 震荡指标
- **RSI**: 相对强弱指标 (14日)
- **Stochastic**: 随机指标 (14-3-3)

### 波动性指标
- **Bollinger Bands**: 布林带 (20日, 2倍标准差)
- **ATR**: 平均真实波幅 (14日)

### 成交量指标
- **OBV**: 能量潮指标

### 数据输出特点
- **纯数值数据**: 组合数据文件只包含原始价格数据和技术指标数值
- **无信号分析**: 移除了文本形式的信号分析列，便于数值计算
- **完整指标**: 保留所有重要的技术指标数值
- **参数优化**: 根据不同时间周期自动使用优化的技术指标参数

### 技术指标参数优化

系统针对不同时间周期使用了优化的技术指标参数：

**15分钟线（短线交易）**：
- 移动平均线：MA10/MA30（更敏感）
- MACD：(8,17,9)（快速响应）
- RSI：9周期（敏感超买超卖）
- 布林带：15周期（短期波动）

**1小时线（短中期分析）**：
- 移动平均线：MA12/MA26（平衡敏感性）
- MACD：(9,21,7)（适中响应）
- RSI：11周期（平衡指标）
- 布林带：16周期（适中波动）

**4小时线（中期分析）**：
- 移动平均线：MA20/MA50（标准参数）
- MACD：(12,26,9)（经典参数）
- RSI：14周期（标准参数）
- 布林带：20周期（标准参数）

**日线（长期分析）**：
- 移动平均线：MA21/MA55（稍长周期）
- MACD：(12,26,9)（标准参数）
- RSI：14周期（标准参数）
- 布林带：21周期（减少噪音）

## 安装配置

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置API密钥
在项目根目录创建 `.env` 文件：
```
BINANCE_API_KEY=your_api_key_here
BINANCE_API_SECRET=your_api_secret_here
```

### 3. 运行程序
```bash
python main.py
```

## 使用说明

运行程序后，选择以下选项：

1. **执行完整分析流程** - 选择时间周期后运行完整的数据获取→指标计算→数据合并→报告生成流程
2. **显示文件路径信息** - 查看生成文件的位置和状态
3. **运行测试模式** - 测试各个模块的功能
4. **退出** - 退出程序

### 时间周期选择

选择选项1后，系统会提示选择K线时间周期：
- **15分钟线**: 适合短线交易和日内分析
- **1小时线**: 适合短中期分析和波段交易
- **4小时线**: 适合中期趋势分析
- **日线**: 适合长期趋势分析

## 输出文件

系统会在 `data/` 目录下生成以下文件：

- `BTCUSDT_日线原始数据_YYYYMMDD.csv` - 原始K线数据
- `BTCUSDT_技术指标分析_YYYYMMDD.csv` - 技术指标计算结果
- `BTCUSDT_组合数据_YYYYMMDD.csv` - 原始数据+技术指标的完整数据集（24列纯数值数据）
- `BTCUSDT_交易分析报告_YYYYMMDD.txt` - 易于DeepSeek理解的分析报告

### 组合数据文件列结构（24列）

**基础数据（10列）**：
- `open_time` - 开盘时间
- `开盘价`, `最高价`, `最低价`, `收盘价` - OHLC价格数据
- `成交量`, `成交额`, `成交笔数` - 成交量相关数据
- `主动买入量`, `主动买入额` - 主动买入数据

**技术指标（14列）**：
- `MA20`, `MA50` - 移动平均线
- `MACD`, `MACD_Signal`, `MACD_Hist` - MACD指标
- `RSI` - 相对强弱指标
- `BB_Upper`, `BB_Middle`, `BB_Lower` - 布林带
- `Stoch_SlowK`, `Stoch_SlowD` - 随机指标
- `OBV` - 能量潮指标
- `ATR` - 平均真实波幅
- `ADX` - 平均趋向指数

## 项目结构

```
day_binance/
├── main.py                    # 主程序入口
├── config.py                  # 配置管理
├── binance_client.py          # 币安API客户端
├── ta_calculator.py           # 技术指标计算
├── combined_data_processor.py # 数据合并处理
├── report_generator.py        # 分析报告生成
├── requirements.txt           # 依赖包列表
├── .env                       # API密钥配置
├── data/                      # 数据输出目录
└── logs/                      # 日志目录
```

## 注意事项

1. **API密钥安全**: 请妥善保管您的币安API密钥，不要提交到版本控制系统
2. **网络连接**: 确保网络可以访问币安API (https://fapi.binance.com)
3. **时间同步**: 系统时间需要准确，币安API要求时间误差在30秒内
4. **数据更新**: 根据选择的时间周期获取相应数量的最新数据

## 故障排除

如果遇到问题，请检查：
- API密钥是否正确配置
- 网络连接是否正常
- 系统时间是否准确
- 所有依赖包是否正确安装

## DeepSeek AI 使用建议

生成的分析报告专为DeepSeek AI优化，包含：
- 结构化的技术指标数据
- 清晰的信号分析
- 具体的交易建议
- 风险管理提示

将报告全文发送给DeepSeek AI可获得更详细的市场分析和交易策略建议。
