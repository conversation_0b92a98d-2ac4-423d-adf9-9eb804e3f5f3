"""
技术指标计算验证脚本
检查不同时间周期下技术指标计算的准确性和一致性
"""

import pandas as pd
import numpy as np
import talib
from pathlib import Path

def validate_timeframe_indicators():
    """验证不同时间周期的技术指标计算"""
    
    print("=" * 60)
    print("技术指标计算验证报告")
    print("=" * 60)
    
    # 定义时间周期和对应文件
    timeframes = {
        '15分钟线': 'data/BTCUSDT_15分钟线组合数据_20250717.csv',
        '1小时线': 'data/BTCUSDT_1小时线组合数据_20250717.csv', 
        '4小时线': 'data/BTCUSDT_4小时线组合数据_20250717.csv',
        '日线': 'data/BTCUSDT_组合数据_20250717.csv'
    }
    
    # 技术指标参数
    indicators_config = {
        'MA20': {'period': 20, 'min_data': 20},
        'MA50': {'period': 50, 'min_data': 50},
        'MACD': {'params': (12, 26, 9), 'min_data': 35},
        'RSI': {'period': 14, 'min_data': 14},
        'BB': {'period': 20, 'min_data': 20},
        'ATR': {'period': 14, 'min_data': 14}
    }
    
    results = {}
    
    for timeframe, filepath in timeframes.items():
        print(f"\n📊 {timeframe} 验证:")
        print("-" * 40)
        
        try:
            # 读取数据
            if not Path(filepath).exists():
                print(f"❌ 文件不存在: {filepath}")
                continue
                
            df = pd.read_csv(filepath)
            data_count = len(df)
            print(f"数据量: {data_count}条")
            
            # 验证每个指标
            timeframe_results = {}
            
            for indicator, config in indicators_config.items():
                min_data = config['min_data']
                
                if data_count < min_data:
                    print(f"⚠️  {indicator}: 数据不足 (需要{min_data}条，实际{data_count}条)")
                    timeframe_results[indicator] = 'insufficient_data'
                    continue
                
                # 检查指标列是否存在
                indicator_cols = {
                    'MA20': ['MA20'],
                    'MA50': ['MA50'], 
                    'MACD': ['MACD', 'MACD_Signal', 'MACD_Hist'],
                    'RSI': ['RSI'],
                    'BB': ['BB_Upper', 'BB_Middle', 'BB_Lower'],
                    'ATR': ['ATR']
                }
                
                missing_cols = []
                for col in indicator_cols[indicator]:
                    if col not in df.columns:
                        missing_cols.append(col)
                
                if missing_cols:
                    print(f"❌ {indicator}: 缺少列 {missing_cols}")
                    timeframe_results[indicator] = 'missing_columns'
                    continue
                
                # 检查有效数据量
                valid_counts = []
                for col in indicator_cols[indicator]:
                    valid_count = df[col].notna().sum()
                    valid_counts.append(valid_count)
                
                min_valid = min(valid_counts)
                expected_valid = data_count - min_data + 1
                
                if min_valid >= expected_valid * 0.9:  # 允许10%的误差
                    print(f"✅ {indicator}: 正常 (有效数据{min_valid}条)")
                    timeframe_results[indicator] = 'ok'
                else:
                    print(f"⚠️  {indicator}: 有效数据偏少 (期望≥{expected_valid}，实际{min_valid})")
                    timeframe_results[indicator] = 'low_valid_data'
            
            # 验证指标值的合理性
            print("\n指标值合理性检查:")
            latest = df.iloc[-1]
            
            # MA20应该小于等于MA50在下跌趋势中，或者相反
            ma20 = latest.get('MA20')
            ma50 = latest.get('MA50')
            if pd.notna(ma20) and pd.notna(ma50):
                print(f"  MA20: {ma20:.2f}, MA50: {ma50:.2f}")
                
            # RSI应该在0-100之间
            rsi = latest.get('RSI')
            if pd.notna(rsi):
                if 0 <= rsi <= 100:
                    print(f"  RSI: {rsi:.2f} ✅")
                else:
                    print(f"  RSI: {rsi:.2f} ❌ (超出0-100范围)")
            
            # 布林带上轨应该大于下轨
            bb_upper = latest.get('BB_Upper')
            bb_lower = latest.get('BB_Lower')
            if pd.notna(bb_upper) and pd.notna(bb_lower):
                if bb_upper > bb_lower:
                    print(f"  布林带: 上轨{bb_upper:.2f} > 下轨{bb_lower:.2f} ✅")
                else:
                    print(f"  布林带: 上轨{bb_upper:.2f} <= 下轨{bb_lower:.2f} ❌")
            
            results[timeframe] = timeframe_results
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            results[timeframe] = 'error'
    
    # 总结报告
    print("\n" + "=" * 60)
    print("验证总结")
    print("=" * 60)
    
    all_ok = True
    for timeframe, timeframe_results in results.items():
        if timeframe_results == 'error':
            print(f"❌ {timeframe}: 处理错误")
            all_ok = False
        else:
            issues = [indicator for indicator, status in timeframe_results.items() if status != 'ok']
            if issues:
                print(f"⚠️  {timeframe}: 存在问题的指标 - {', '.join(issues)}")
                all_ok = False
            else:
                print(f"✅ {timeframe}: 所有指标正常")
    
    if all_ok:
        print(f"\n🎉 结论: 所有时间周期的技术指标计算都正常！")
    else:
        print(f"\n⚠️  结论: 部分时间周期存在问题，需要检查")
    
    return results

if __name__ == "__main__":
    validate_timeframe_indicators()
